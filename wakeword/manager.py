#!/usr/bin/env python3
"""
Wakeword Manager for ASR Integration
Manages CNN PyTorch-based wakeword detection and ASR activation
"""

import asyncio
import logging
import threading
import time
from typing import Callable, Optional
from .wakeword_detector import CNNWakewordDetector


class WakewordManager:
    """
    Manager class that coordinates CNN wakeword detection with ASR
    """
    
    def __init__(self, 
                 asr_callback: Callable = None,
                 confidence_threshold: float = 0.5,
                 detection_timeout: float = 5.0,
                 enabled: bool = True):
        """
        Initialize wakeword manager
        
        Args:
            asr_callback: Callback function to trigger ASR when wake word detected
            confidence_threshold: Minimum confidence for detection
            detection_timeout: Seconds to wait after detection before re-enabling
            enabled: Whether wakeword detection is enabled
        """
        self.asr_callback = asr_callback
        self.confidence_threshold = confidence_threshold
        self.detection_timeout = detection_timeout
        self.enabled = enabled
        
        self.detector = None
        self.is_active = False
        self.detection_lock = threading.Lock()
        self.last_detection_time = 0
        
        # State management
        self.asr_active = False
        self.detection_count = 0
        
        if self.enabled:
            self._setup_detector()
    
    def _setup_detector(self):
        """Setup the CNN wakeword detector"""
        try:
            self.detector = CNNWakewordDetector(
                callback=self._on_wakeword_detected,
                confidence_threshold=self.confidence_threshold
            )
            
            if self.detector.model is None:
                logging.error("❌ Failed to load CNN wakeword model")
                self.enabled = False
                return False
            
            logging.info("✅ CNN wakeword detector initialized")
            return True
            
        except Exception as e:
            logging.error(f"❌ Failed to setup CNN wakeword detector: {e}")
            self.enabled = False
            return False
    
    def _on_wakeword_detected(self, keyword: str):
        """
        Callback when wake word is detected
        
        Args:
            keyword: The detected wake word
        """
        with self.detection_lock:
            current_time = time.time()
            
            # Check timeout to prevent rapid re-triggering
            if current_time - self.last_detection_time < self.detection_timeout:
                logging.debug(f"Wake word detection ignored (timeout not elapsed)")
                return
            
            self.last_detection_time = current_time
            self.detection_count += 1
            
            logging.info(f"🎤 WAKE WORD DETECTED: '{keyword}' (count: {self.detection_count})")
            
            # Trigger ASR if callback is provided
            if self.asr_callback and not self.asr_active:
                try:
                    self.asr_active = True
                    logging.info("🚀 Activating ASR after wake word detection")
                    
                    # Call ASR activation callback
                    if asyncio.iscoroutinefunction(self.asr_callback):
                        # If callback is async, schedule it
                        loop = None
                        try:
                            loop = asyncio.get_running_loop()
                        except RuntimeError:
                            pass
                        
                        if loop:
                            asyncio.create_task(self.asr_callback(keyword))
                        else:
                            # No event loop running, create new one
                            asyncio.run(self.asr_callback(keyword))
                    else:
                        # Synchronous callback
                        self.asr_callback(keyword)
                        
                except Exception as e:
                    logging.error(f"Error in ASR callback: {e}")
                    self.asr_active = False
    
    def start(self):
        """Start wake word detection"""
        if not self.enabled or not self.detector:
            logging.info("Wake word detection not started (disabled or no detector)")
            return False
        
        if self.is_active:
            logging.warning("Wake word detection already active")
            return True
        
        try:
            success = self.detector.start_listening()
            if success:
                self.is_active = True
                logging.info("✅ CNN Wake word detection started")
                return True
            else:
                logging.error("❌ Failed to start CNN wake word detection")
                return False
            
        except Exception as e:
            logging.error(f"Failed to start CNN wake word detection: {e}")
            return False
    
    def stop(self):
        """Stop wake word detection"""
        if not self.is_active:
            return
        
        try:
            if self.detector:
                self.detector.stop_listening()
            self.is_active = False
            self.asr_active = False
            logging.info("⏹️ CNN Wake word detection stopped")
            
        except Exception as e:
            logging.error(f"Error stopping CNN wake word detection: {e}")
    
    def reset_asr_state(self):
        """Reset ASR active state (call this when ASR processing is complete)"""
        with self.detection_lock:
            self.asr_active = False
            logging.debug("ASR state reset - ready for next wake word")
    
    def get_stats(self) -> dict:
        """Get detection statistics"""
        return {
            "enabled": self.enabled,
            "active": self.is_active,
            "asr_active": self.asr_active,
            "detection_count": self.detection_count,
            "last_detection_time": self.last_detection_time,
            "confidence_threshold": self.confidence_threshold,
            "detection_timeout": self.detection_timeout,
            "model_loaded": self.detector.model is not None if self.detector else False,
            "model_type": "CNN_PyTorch"
        }
    
    def set_confidence_threshold(self, threshold: float):
        """Update confidence threshold"""
        self.confidence_threshold = threshold
        if self.detector:
            self.detector.confidence_threshold = threshold
        logging.info(f"Updated confidence threshold to {threshold}")
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.stop()


# Utility function for easy integration
def create_wakeword_manager(
    asr_callback: Callable = None,
    confidence_threshold: float = 0.5,
    detection_timeout: float = 5.0,
    enabled: bool = True
) -> WakewordManager:
    """
    Create a wakeword manager with custom configuration
    
    Args:
        asr_callback: Callback to trigger ASR
        confidence_threshold: Minimum confidence for detection
        detection_timeout: Timeout between detections
        enabled: Whether to enable wakeword detection
    
    Returns:
        WakewordManager instance
    """
    return WakewordManager(
        asr_callback=asr_callback,
        confidence_threshold=confidence_threshold,
        detection_timeout=detection_timeout,
        enabled=enabled
    )
