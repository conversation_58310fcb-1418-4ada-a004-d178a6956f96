#!/usr/bin/env python3
"""
Wakeword Detection using CNN PyTorch Model from 06_training
Based on the trained "Hây ka ka" CNN detection model
"""

import os
import numpy as np
import torch
import torch.nn as nn
import time
import logging
from pathlib import Path
import json
from typing import Callable


class WakewordCNN(nn.Module):
    """1D CNN for wakeword detection (same as training)"""

    def __init__(self, input_length=32000):
        super(WakewordCNN, self).__init__()

        # First conv block
        self.conv1 = nn.Conv1d(1, 32, kernel_size=80, stride=4, padding=38)
        self.bn1 = nn.BatchNorm1d(32)
        self.pool1 = nn.MaxPool1d(4)
        self.dropout1 = nn.Dropout(0.2)

        # Second conv block
        self.conv2 = nn.Conv1d(32, 64, kernel_size=3, stride=1, padding=1)
        self.bn2 = nn.BatchNorm1d(64)
        self.pool2 = nn.MaxPool1d(4)
        self.dropout2 = nn.Dropout(0.2)

        # Third conv block
        self.conv3 = nn.Conv1d(64, 128, kernel_size=3, stride=1, padding=1)
        self.bn3 = nn.BatchNorm1d(128)
        self.pool3 = nn.MaxPool1d(4)
        self.dropout3 = nn.Dropout(0.3)

        # Fourth conv block
        self.conv4 = nn.Conv1d(128, 256, kernel_size=3, stride=1, padding=1)
        self.bn4 = nn.BatchNorm1d(256)
        self.pool4 = nn.MaxPool1d(4)
        self.dropout4 = nn.Dropout(0.3)

        # Calculate the size after convolutions
        self.feature_size = self._get_conv_output_size(input_length)

        # Fully connected layers
        self.fc1 = nn.Linear(self.feature_size, 512)
        self.fc_dropout1 = nn.Dropout(0.5)
        self.fc2 = nn.Linear(512, 128)
        self.fc_dropout2 = nn.Dropout(0.3)
        self.fc3 = nn.Linear(128, 1)

        # Activation functions
        self.relu = nn.ReLU()
        self.sigmoid = nn.Sigmoid()

    def _get_conv_output_size(self, input_length):
        """Calculate the output size after all conv layers"""
        # Simulate forward pass to get size
        x = torch.randn(1, 1, input_length)
        relu = nn.ReLU()
        x = self.pool1(relu(self.bn1(self.conv1(x))))
        x = self.pool2(relu(self.bn2(self.conv2(x))))
        x = self.pool3(relu(self.bn3(self.conv3(x))))
        x = self.pool4(relu(self.bn4(self.conv4(x))))
        return x.view(1, -1).size(1)

    def forward(self, x):
        # Conv layers
        x = self.dropout1(self.pool1(self.relu(self.bn1(self.conv1(x)))))
        x = self.dropout2(self.pool2(self.relu(self.bn2(self.conv2(x)))))
        x = self.dropout3(self.pool3(self.relu(self.bn3(self.conv3(x)))))
        x = self.dropout4(self.pool4(self.relu(self.bn4(self.conv4(x)))))

        # Flatten
        x = x.view(x.size(0), -1)

        # FC layers
        x = self.fc_dropout1(self.relu(self.fc1(x)))
        x = self.fc_dropout2(self.relu(self.fc2(x)))
        x = self.sigmoid(self.fc3(x))

        return x


class CNNWakewordDetector:
    """
    Wakeword detector using the trained CNN PyTorch model from 06_training
    """

    def __init__(self,
                 model_path: str = None,
                 model_info_path: str = None,
                 callback: Callable[[str], None] = None,
                 confidence_threshold: float = 0.5):
        """
        Initialize CNN wakeword detector

        Args:
            model_path: Path to PyTorch model file
            model_info_path: Path to model info JSON
            callback: Function to call when wakeword detected
            confidence_threshold: Minimum confidence for detection
        """
        self.model = None
        self.model_info = None
        self.callback = callback
        self.confidence_threshold = confidence_threshold

        # Audio parameters (from training)
        self.sample_rate = 16000
        self.duration = 2.0
        self.n_samples = int(self.sample_rate * self.duration)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Detection state
        self.is_listening = False

        # Load model
        if model_path:
            self.load_model(model_path, model_info_path)
        else:
            self.find_and_load_model()
    
    def find_and_load_model(self):
        """Find and load the CNN PyTorch model from models directory"""
        # Look for models in models directory
        models_dir = Path("models")
        if not models_dir.exists():
            logging.error("❌ models directory not found")
            return False

        # Find the specific CNN model file
        model_file = models_dir / "wakeword_hay_ka_ka_cnn.pth"
        info_file = models_dir / "wakeword_hay_ka_ka_cnn_info.json"

        if not model_file.exists():
            logging.error(f"❌ CNN model file not found: {model_file}")
            return False

        return self.load_model(model_file, info_file)
    
    def load_model(self, model_path, info_path=None):
        """Load trained CNN model directly"""
        try:
            logging.info(f"📥 Loading CNN wakeword model: {model_path}")

            # Load model info first
            if info_path and Path(info_path).exists():
                with open(info_path, 'r') as f:
                    self.model_info = json.load(f)
                    self.sample_rate = self.model_info.get('sample_rate', 16000)
                    self.duration = self.model_info.get('duration', 2.0)
                    self.n_samples = self.model_info.get('n_samples', 32000)

            # Try loading the complete model first
            try:
                self.model = torch.load(model_path, map_location=self.device)
                self.model.eval()
                logging.info(f"✅ CNN model loaded directly")
            except Exception as e1:
                logging.info(f"Direct load failed, trying state dict: {e1}")
                # Fallback: load as state dict
                self.model = WakewordCNN(input_length=self.n_samples).to(self.device)
                state_dict = torch.load(model_path, map_location=self.device)
                self.model.load_state_dict(state_dict)
                self.model.eval()
                logging.info(f"✅ CNN model loaded via state dict")

            if self.model_info:
                logging.info(f"   Model type: {self.model_info.get('model_type', 'CNN_PyTorch')}")
                logging.info(f"   Sample rate: {self.sample_rate}Hz")
                logging.info(f"   Duration: {self.duration}s")
                logging.info(f"   Input size: {self.n_samples}")
            else:
                logging.info(f"✅ CNN model loaded (using default parameters)")

            return True

        except Exception as e:
            logging.error(f"❌ Error loading CNN wakeword model: {e}")
            return False
    
    def preprocess_audio(self, audio):
        """Preprocess audio for CNN model input (same as training)"""
        try:
            # Ensure correct length
            if len(audio) < self.n_samples:
                audio = np.pad(audio, (0, self.n_samples - len(audio)))
            elif len(audio) > self.n_samples:
                audio = audio[:self.n_samples]

            # Normalize
            audio = audio / (np.max(np.abs(audio)) + 1e-8)

            return audio

        except Exception as e:
            logging.error(f"❌ Error preprocessing audio: {e}")
            return None
    
    def predict_audio(self, audio):
        """Predict if audio contains wakeword using CNN"""
        if self.model is None:
            logging.error("❌ No CNN model loaded")
            return False, 0.0

        # Preprocess audio
        audio_processed = self.preprocess_audio(audio)
        if audio_processed is None:
            return False, 0.0

        try:
            # Convert to tensor and add appropriate dimensions
            # CNN expects (batch, channels, length)
            audio_tensor = torch.FloatTensor(audio_processed).unsqueeze(0).unsqueeze(0).to(self.device)

            # Predict
            with torch.no_grad():
                output = self.model(audio_tensor)
                probability = output.item()

            # Convert to binary prediction
            is_wakeword = probability > 0.5
            confidence = probability if is_wakeword else (1 - probability)

            return is_wakeword, confidence

        except Exception as e:
            logging.error(f"❌ Error in CNN prediction: {e}")
            return False, 0.0
    
    def process_audio_chunk(self, audio_chunk):
        """Process audio chunk from WebSocket for wakeword detection"""
        if not self.is_listening or self.model is None:
            return False, 0.0

        try:
            # Initialize buffer if not exists
            if not hasattr(self, 'audio_buffer'):
                self.audio_buffer = np.array([])
                self.chunk_count = 0

            # Add new audio chunk to buffer
            if isinstance(audio_chunk, np.ndarray):
                audio_data = audio_chunk.flatten()
            else:
                audio_data = np.array(audio_chunk, dtype=np.float32).flatten()

            self.audio_buffer = np.concatenate([self.audio_buffer, audio_data])
            self.chunk_count += 1

            # Prevent memory leak - reset chunk count periodically
            if self.chunk_count > 10000:
                self.chunk_count = 0



            # Keep buffer at fixed size (sliding window)
            if len(self.audio_buffer) > self.n_samples:
                self.audio_buffer = self.audio_buffer[-self.n_samples:]

            # Only process if buffer is full enough
            if len(self.audio_buffer) >= self.n_samples:
                # Predict on current buffer using CNN
                prediction, confidence = self.predict_audio(self.audio_buffer)



                if prediction and confidence >= self.confidence_threshold:
                    # Check if enough time has passed since last detection
                    current_time = time.time()
                    if not hasattr(self, 'last_detection_time'):
                        self.last_detection_time = 0

                    if current_time - self.last_detection_time > 2.0:  # 2 second cooldown
                        self.last_detection_time = current_time
                        logging.info(f"🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: {confidence:.3f})")

                        if self.callback:
                            self.callback("Hây ka ka")

                        # Clear buffer to avoid repeated detections
                        self.audio_buffer = np.array([])
                        self.chunk_count = 0
                        return True, confidence

                return prediction, confidence

            return False, 0.0

        except Exception as e:
            logging.error(f"❌ CNN audio processing error: {e}")
            return False, 0.0
    
    def start_listening(self):
        """Start listening for wake words using CNN (WebSocket mode)"""
        if self.model is None:
            logging.error("❌ Cannot start listening: no CNN model loaded")
            return False

        if self.is_listening:
            logging.warning("⚠️ Already listening for wake words")
            return True

        try:
            self.is_listening = True

            # Initialize audio buffer for WebSocket audio processing
            self.audio_buffer = np.array([])
            self.last_detection_time = 0
            self.chunk_count = 0

            logging.info("✅ Started CNN listening for wakeword 'Hây ka ka' (WebSocket mode)")
            return True

        except Exception as e:
            logging.error(f"❌ Failed to start CNN wakeword detection: {e}")
            self.stop_listening()
            return False
    
    def stop_listening(self):
        """Stop listening for wake words"""
        if not self.is_listening:
            return

        self.is_listening = False

        # Clear audio buffer
        if hasattr(self, 'audio_buffer'):
            self.audio_buffer = np.array([])

        logging.info("⏹️ Stopped CNN listening for wake words")

    def __del__(self):
        """Cleanup resources"""
        self.stop_listening()
