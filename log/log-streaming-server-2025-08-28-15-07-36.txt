2025-08-28 15:07:41,315 INFO [wakeword_detector.py:162] 📥 Loading CNN wakeword model: 06_training/models/wakeword_hay_ka_ka_cnn.pth
2025-08-28 15:07:41,329 INFO [wakeword_detector.py:178] Direct load failed, trying state dict: 'collections.OrderedDict' object has no attribute 'eval'
2025-08-28 15:07:41,367 INFO [wakeword_detector.py:184] ✅ CNN model loaded via state dict
2025-08-28 15:07:41,367 INFO [wakeword_detector.py:187]    Model type: CNN_PyTorch
2025-08-28 15:07:41,367 INFO [wakeword_detector.py:188]    Sample rate: 16000Hz
2025-08-28 15:07:41,367 INFO [wakeword_detector.py:189]    Duration: 2.0s
2025-08-28 15:07:41,367 INFO [wakeword_detector.py:190]    Input size: 32000
2025-08-28 15:07:41,367 INFO [manager.py:64] ✅ CNN wakeword detector initialized
2025-08-28 15:07:41,367 INFO [main.py:269] ✅ CNN Wakeword manager initialized
2025-08-28 15:07:41,367 INFO [wakeword_detector.py:312] ✅ Started CNN listening for wakeword 'Hây ka ka'
2025-08-28 15:07:41,369 INFO [manager.py:134] ✅ CNN Wake word detection started
2025-08-28 15:07:41,369 INFO [main.py:237] ✅ Wakeword detection started successfully
2025-08-28 15:07:41,381 INFO [server.py:341] server listening on [::]:8004
2025-08-28 15:07:41,381 INFO [server.py:341] server listening on 0.0.0.0:8004
2025-08-28 15:07:41,381 INFO [main.py:249] 🚀 Streaming server started at port 8004
2025-08-28 15:07:41,381 INFO [main.py:251] 🎤 Wakeword detection active with keyword: 'Hây ka ka'
2025-08-28 15:07:53,540 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.525)
2025-08-28 15:07:53,541 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 1)
2025-08-28 15:07:53,541 INFO [manager.py:96] 🚀 Activating ASR after wake word detection
2025-08-28 15:07:53,541 INFO [main.py:142] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' - ASR is now active
2025-08-28 15:07:53,541 INFO [main.py:143] 📊 Wakeword Stats: {'enabled': True, 'active': True, 'asr_active': True, 'detection_count': 1, 'last_detection_time': 1756368473.5410619, 'confidence_threshold': 0.5, 'detection_timeout': 5, 'model_loaded': True, 'model_type': 'CNN_PyTorch'}
2025-08-28 15:08:00,386 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.557)
2025-08-28 15:08:00,386 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 2)
2025-08-28 15:08:10,592 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.864)
2025-08-28 15:08:10,592 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 3)
2025-08-28 15:08:21,199 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.584)
2025-08-28 15:08:21,200 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 4)
2025-08-28 15:08:40,753 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.700)
2025-08-28 15:08:40,754 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 5)
2025-08-28 15:08:49,144 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.960)
2025-08-28 15:08:49,144 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 6)
2025-08-28 15:08:59,329 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.514)
2025-08-28 15:08:59,330 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 7)
2025-08-28 15:09:07,336 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.841)
2025-08-28 15:09:07,336 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 8)
2025-08-28 15:09:17,007 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.684)
2025-08-28 15:09:17,009 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 9)
2025-08-28 15:09:32,370 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.945)
2025-08-28 15:09:32,371 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 10)
2025-08-28 15:09:39,606 INFO [wakeword_detector.py:282] 🎤 CNN WAKEWORD DETECTED: 'Hây ka ka' (confidence: 0.798)
2025-08-28 15:09:39,607 INFO [manager.py:90] 🎤 WAKE WORD DETECTED: 'Hây ka ka' (count: 11)
2025-08-28 15:09:40,698 INFO [server.py:440] server closing
2025-08-28 15:09:40,699 INFO [server.py:473] server closed
2025-08-28 15:09:40,700 INFO [main.py:287] 🛑 Server stopped by user
2025-08-28 15:09:40,946 INFO [wakeword_detector.py:331] ⏹️ Stopped CNN listening for wake words
2025-08-28 15:09:40,946 INFO [manager.py:154] ⏹️ CNN Wake word detection stopped
2025-08-28 15:09:40,946 INFO [main.py:292] 🧹 CNN Wakeword manager stopped
