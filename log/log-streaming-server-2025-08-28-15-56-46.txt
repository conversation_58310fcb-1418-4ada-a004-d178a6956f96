2025-08-28 15:56:50,013 INFO [wakeword_detector.py:162] 📥 Loading CNN wakeword model: 06_training/models/wakeword_hay_ka_ka_cnn.pth
2025-08-28 15:56:50,027 INFO [wakeword_detector.py:178] Direct load failed, trying state dict: 'collections.OrderedDict' object has no attribute 'eval'
2025-08-28 15:56:50,068 INFO [wakeword_detector.py:184] ✅ CNN model loaded via state dict
2025-08-28 15:56:50,068 INFO [wakeword_detector.py:187]    Model type: CNN_PyTorch
2025-08-28 15:56:50,068 INFO [wakeword_detector.py:188]    Sample rate: 16000Hz
2025-08-28 15:56:50,068 INFO [wakeword_detector.py:189]    Duration: 2.0s
2025-08-28 15:56:50,068 INFO [wakeword_detector.py:190]    Input size: 32000
2025-08-28 15:56:50,068 INFO [manager.py:64] ✅ CNN wakeword detector initialized
2025-08-28 15:56:50,068 INFO [main.py:277] ✅ CNN Wakeword manager initialized
2025-08-28 15:56:50,069 INFO [wakeword_detector.py:318] ✅ Started CNN listening for wakeword 'Hây ka ka' (WebSocket mode)
2025-08-28 15:56:50,069 INFO [manager.py:134] ✅ CNN Wake word detection started
2025-08-28 15:56:50,069 INFO [main.py:245] ✅ Wakeword detection initialized for WebSocket audio
2025-08-28 15:56:50,092 INFO [server.py:341] server listening on 0.0.0.0:8004
2025-08-28 15:56:50,092 INFO [server.py:341] server listening on [::]:8004
2025-08-28 15:56:50,092 INFO [main.py:257] 🚀 Streaming server started at port 8004
2025-08-28 15:56:50,092 INFO [main.py:259] 🎤 Wakeword detection active with keyword: 'Hây ka ka' (WebSocket mode)
2025-08-28 15:59:38,860 INFO [server.py:440] server closing
2025-08-28 15:59:38,865 INFO [server.py:473] server closed
2025-08-28 15:59:38,867 INFO [main.py:295] 🛑 Server stopped by user
2025-08-28 15:59:38,869 INFO [wakeword_detector.py:337] ⏹️ Stopped CNN listening for wake words
2025-08-28 15:59:38,869 INFO [manager.py:154] ⏹️ CNN Wake word detection stopped
2025-08-28 15:59:38,870 INFO [main.py:300] 🧹 CNN Wakeword manager stopped
