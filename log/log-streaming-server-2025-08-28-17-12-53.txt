2025-08-28 17:12:59,761 INFO [wakeword_detector.py:157] 📥 Loading CNN wakeword model: 06_training/models/wakeword_hay_ka_ka_cnn.pth
2025-08-28 17:12:59,777 INFO [wakeword_detector.py:173] Direct load failed, trying state dict: 'collections.OrderedDict' object has no attribute 'eval'
2025-08-28 17:12:59,820 INFO [wakeword_detector.py:179] ✅ CNN model loaded via state dict
2025-08-28 17:12:59,820 INFO [wakeword_detector.py:182]    Model type: CNN_PyTorch
2025-08-28 17:12:59,820 INFO [wakeword_detector.py:183]    Sample rate: 16000Hz
2025-08-28 17:12:59,820 INFO [wakeword_detector.py:184]    Duration: 2.0s
2025-08-28 17:12:59,820 INFO [wakeword_detector.py:185]    Input size: 32000
2025-08-28 17:12:59,820 INFO [manager.py:64] ✅ CNN wakeword detector initialized
2025-08-28 17:12:59,820 INFO [main.py:298] ✅ CNN Wakeword manager initialized
2025-08-28 17:12:59,820 DEBUG [selector_events.py:59] Using selector: KqueueSelector
2025-08-28 17:12:59,821 DEBUG [selector_events.py:59] Using selector: KqueueSelector
2025-08-28 17:12:59,821 INFO [wakeword_detector.py:325] ✅ Started CNN listening for wakeword 'Hây ka ka' (WebSocket mode)
2025-08-28 17:12:59,821 INFO [manager.py:134] ✅ CNN Wake word detection started
2025-08-28 17:12:59,821 INFO [main.py:266] ✅ Wakeword detection initialized for WebSocket audio
2025-08-28 17:12:59,836 INFO [server.py:341] server listening on [::]:8004
2025-08-28 17:12:59,836 INFO [server.py:341] server listening on 0.0.0.0:8004
2025-08-28 17:12:59,836 INFO [main.py:278] 🚀 Streaming server started at port 8004
2025-08-28 17:12:59,836 INFO [main.py:280] 🎤 Wakeword detection active with keyword: 'Hây ka ka' (WebSocket mode)
