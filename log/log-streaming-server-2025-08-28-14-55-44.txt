2025-08-28 14:55:49,128 INFO [wakeword_detector.py:162] 📥 Loading CNN wakeword model: 06_training/models/wakeword_hay_ka_ka_cnn.pth
2025-08-28 14:55:49,172 INFO [wakeword_detector.py:180] ✅ CNN wakeword model loaded successfully
2025-08-28 14:55:49,172 INFO [wakeword_detector.py:182]    Model type: CNN_PyTorch
2025-08-28 14:55:49,172 INFO [wakeword_detector.py:183]    Sample rate: 16000Hz
2025-08-28 14:55:49,173 INFO [wakeword_detector.py:184]    Duration: 2.0s
2025-08-28 14:55:49,173 INFO [wakeword_detector.py:185]    Input size: 32000
2025-08-28 14:55:49,173 INFO [manager.py:64] ✅ CNN wakeword detector initialized
2025-08-28 14:55:49,173 INFO [main.py:269] ✅ CNN Wakeword manager initialized
2025-08-28 14:55:49,174 INFO [wakeword_detector.py:307] ✅ Started CNN listening for wakeword '<PERSON><PERSON><PERSON> ka ka'
2025-08-28 14:55:49,174 INFO [manager.py:134] ✅ CNN Wake word detection started
2025-08-28 14:55:49,175 INFO [main.py:237] ✅ Wakeword detection started successfully
2025-08-28 14:55:49,314 INFO [server.py:341] server listening on [::]:8004
2025-08-28 14:55:49,314 INFO [server.py:341] server listening on 0.0.0.0:8004
2025-08-28 14:55:49,315 INFO [main.py:249] 🚀 Streaming server started at port 8004
2025-08-28 14:55:49,315 INFO [server.py:440] server closing
2025-08-28 14:55:49,315 INFO [server.py:473] server closed
2025-08-28 14:55:49,599 INFO [wakeword_detector.py:326] ⏹️ Stopped CNN listening for wake words
2025-08-28 14:55:49,599 INFO [manager.py:154] ⏹️ CNN Wake word detection stopped
2025-08-28 14:55:49,599 INFO [main.py:292] 🧹 CNN Wakeword manager stopped
