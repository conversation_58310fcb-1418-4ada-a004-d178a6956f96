2025-08-28 17:44:09,655 INFO [wakeword_detector.py:151] 📥 Loading CNN wakeword model: models/wakeword_hay_ka_ka_cnn.pth
2025-08-28 17:44:09,670 INFO [wakeword_detector.py:167] Direct load failed, trying state dict: 'collections.OrderedDict' object has no attribute 'eval'
2025-08-28 17:44:09,724 INFO [wakeword_detector.py:173] ✅ CNN model loaded via state dict
2025-08-28 17:44:09,725 INFO [wakeword_detector.py:176]    Model type: CNN_PyTorch
2025-08-28 17:44:09,725 INFO [wakeword_detector.py:177]    Sample rate: 16000Hz
2025-08-28 17:44:09,725 INFO [wakeword_detector.py:178]    Duration: 2.0s
2025-08-28 17:44:09,725 INFO [wakeword_detector.py:179]    Input size: 32000
2025-08-28 17:44:09,726 INFO [manager.py:64] ✅ CNN wakeword detector initialized
2025-08-28 17:44:09,726 INFO [main.py:333] ✅ CNN Wakeword manager initialized
2025-08-28 17:44:09,727 INFO [wakeword_detector.py:319] ✅ Started CNN listening for wakeword 'Hây ka ka' (WebSocket mode)
2025-08-28 17:44:09,727 INFO [manager.py:134] ✅ CNN Wake word detection started
2025-08-28 17:44:09,727 INFO [main.py:301] ✅ Wakeword detection initialized for WebSocket audio
2025-08-28 17:44:09,821 INFO [server.py:341] server listening on [::]:8004
2025-08-28 17:44:09,821 INFO [server.py:341] server listening on 0.0.0.0:8004
2025-08-28 17:44:09,821 INFO [main.py:313] 🚀 Streaming server started at port 8004
2025-08-28 17:44:09,821 INFO [main.py:315] 🎤 Wakeword detection active with keyword: 'Hây ka ka' (WebSocket mode)
