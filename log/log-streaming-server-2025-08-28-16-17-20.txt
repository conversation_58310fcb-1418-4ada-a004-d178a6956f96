2025-08-28 16:17:25,503 INFO [wakeword_detector.py:157] 📥 Loading CNN wakeword model: 06_training/models/wakeword_hay_ka_ka_cnn.pth
2025-08-28 16:17:25,517 INFO [wakeword_detector.py:173] Direct load failed, trying state dict: 'collections.OrderedDict' object has no attribute 'eval'
2025-08-28 16:17:25,559 INFO [wakeword_detector.py:179] ✅ CNN model loaded via state dict
2025-08-28 16:17:25,559 INFO [wakeword_detector.py:182]    Model type: CNN_PyTorch
2025-08-28 16:17:25,559 INFO [wakeword_detector.py:183]    Sample rate: 16000Hz
2025-08-28 16:17:25,559 INFO [wakeword_detector.py:184]    Duration: 2.0s
2025-08-28 16:17:25,559 INFO [wakeword_detector.py:185]    Input size: 32000
2025-08-28 16:17:25,559 INFO [manager.py:64] ✅ CNN wakeword detector initialized
2025-08-28 16:17:25,559 INFO [main.py:277] ✅ CNN Wakeword manager initialized
2025-08-28 16:17:25,559 INFO [wakeword_detector.py:313] ✅ Started CNN listening for wakeword 'Hây ka ka' (WebSocket mode)
2025-08-28 16:17:25,559 INFO [manager.py:134] ✅ CNN Wake word detection started
2025-08-28 16:17:25,559 INFO [main.py:245] ✅ Wakeword detection initialized for WebSocket audio
2025-08-28 16:17:25,572 INFO [server.py:341] server listening on [::]:8004
2025-08-28 16:17:25,572 INFO [server.py:341] server listening on 0.0.0.0:8004
2025-08-28 16:17:25,572 INFO [main.py:257] 🚀 Streaming server started at port 8004
2025-08-28 16:17:25,572 INFO [main.py:259] 🎤 Wakeword detection active with keyword: 'Hây ka ka' (WebSocket mode)
2025-08-28 16:19:00,249 INFO [server.py:440] server closing
2025-08-28 16:19:00,250 INFO [server.py:473] server closed
2025-08-28 16:19:00,251 INFO [main.py:295] 🛑 Server stopped by user
2025-08-28 16:19:00,252 INFO [wakeword_detector.py:332] ⏹️ Stopped CNN listening for wake words
2025-08-28 16:19:00,252 INFO [manager.py:154] ⏹️ CNN Wake word detection stopped
2025-08-28 16:19:00,252 INFO [main.py:300] 🧹 CNN Wakeword manager stopped
