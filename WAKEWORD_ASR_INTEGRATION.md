# 🎤 CNN Wakeword + ASR Integration

## ✅ **HOÀN THÀNH TÍCH HỢP THÀNH CÔNG!**

Hệ thống ASR đã được tích hợp thành công với CNN wakeword detection sử dụng model "Hây ka ka" từ thư mục `06_training`.

## 🎯 **Tính năng chính**

### **CNN Wakeword Detection**
- ✅ **Model**: CNN PyTorch từ `06_training/wakeword_cnn_pytorch.py`
- ✅ **Keyword**: "Hây ka ka" (tiếng Việt)
- ✅ **Độ chính xác**: <PERSON> (dựa trên model đã train)
- ✅ **Real-time**: Continuous audio monitoring
- ✅ **Confidence threshold**: <PERSON><PERSON> thể điều chỉnh (mặc định 0.5)

### **ASR Integration**
- ✅ **Kích hoạt**: Chỉ xử lý ASR khi detect được wakeword
- ✅ **WebSocket**: <PERSON><PERSON><PERSON> hợ<PERSON> với WebSocket server hiện tại
- ✅ **Logging**: Chi tiết log khi detect wakeword và kích hoạt ASR
- ✅ **State management**: Quản lý trạng thái wakeword và ASR

## 🚀 **Cách sử dụng**

### **1. Cài đặt dependencies**
```bash
pip install -r requirements.txt
```

### **2. Kiểm tra model CNN**
Đảm bảo có model CNN trong `06_training/models/`:
```bash
ls 06_training/models/*cnn*.pth
ls 06_training/models/*cnn*_info.json
```

### **3. Chạy ASR với Wakeword**
```bash
python main.py
```

### **4. Test riêng Wakeword**
```bash
python test_wakeword_asr_integration.py
```

## ⚙️ **Cấu hình**

### **File config.json**
```json
{
  "WAKEWORD_ENABLED": true,
  "WAKEWORD_CONFIDENCE_THRESHOLD": 0.5,
  "WAKEWORD_DETECTION_TIMEOUT": 5.0,
  ...
}
```

### **Tham số có thể điều chỉnh**
- `WAKEWORD_ENABLED`: Bật/tắt wakeword detection
- `WAKEWORD_CONFIDENCE_THRESHOLD`: Ngưỡng confidence (0.0-1.0)
- `WAKEWORD_DETECTION_TIMEOUT`: Thời gian chờ giữa các detection (giây)

## 🔄 **Luồng hoạt động**

### **1. Khởi động**
```
1. Load CNN model từ 06_training/models/
2. Khởi tạo WakewordManager
3. Start WebSocket server
4. Start continuous audio monitoring
```

### **2. Wakeword Detection**
```
1. Continuous audio capture (100ms chunks)
2. Sliding window buffer (2 seconds)
3. CNN prediction trên buffer
4. Nếu confidence > threshold → trigger callback
```

### **3. ASR Activation**
```
1. Wakeword detected → log detection
2. Set ASR active state
3. WebSocket clients nhận thông báo
4. Xử lý ASR bình thường
5. Reset state sau timeout
```

## 📊 **Logging và Monitoring**

### **Log Messages**
- `🎤 CNN WAKEWORD DETECTED`: Khi detect được wakeword
- `🚀 Activating ASR`: Khi kích hoạt ASR
- `🎯 ASR RESULT AFTER CNN WAKEWORD`: Kết quả ASR sau wakeword
- `📊 Wakeword Stats`: Thống kê detection

### **WebSocket Messages**
```json
{
  "status": "waiting_for_wakeword",
  "message": "Waiting for wake word detection...",
  "keywords": ["Hây ka ka"]
}

{
  "text": "result text",
  "segment": 0,
  "wakeword_detected": true,
  "status": "processing"
}
```

## 🧪 **Testing**

### **Test Script**
```bash
python test_wakeword_asr_integration.py
```

### **Test Options**
1. **Interactive test**: Chạy cho đến khi Ctrl+C
2. **Timed test**: Chạy trong thời gian nhất định
3. **Stats monitoring**: Hiển thị thống kê real-time

### **Test Workflow**
1. Say "Hây ka ka" → Wakeword detected
2. System logs detection
3. ASR activated
4. Speak normally → ASR processes speech
5. Results logged
6. System ready for next wakeword

## 📁 **Cấu trúc Files**

```
ASR/
├── main.py                           # Main ASR server với wakeword
├── config.json                       # Cấu hình (đã thêm wakeword)
├── requirements.txt                   # Dependencies (đã thêm PyTorch)
├── test_wakeword_asr_integration.py   # Test script
├── WAKEWORD_ASR_INTEGRATION.md        # Tài liệu này
├── wakeword/                          # Wakeword module
│   ├── __init__.py
│   ├── sklearn_detector.py           # CNN detector (renamed)
│   └── manager.py                     # Wakeword manager
└── 06_training/                       # CNN model training
    └── models/                        # Trained CNN models
        ├── *cnn*.pth                  # CNN model files
        └── *cnn*_info.json           # Model info
```

## 🔧 **Troubleshooting**

### **Common Issues**

1. **No CNN model found**
   ```
   ❌ 06_training/models directory not found
   ```
   **Solution**: Đảm bảo có model CNN đã train trong `06_training/models/`

2. **PyTorch not installed**
   ```
   ❌ Error loading CNN wakeword model
   ```
   **Solution**: `pip install torch>=1.9.0`

3. **Audio device issues**
   ```
   ❌ CNN audio callback error
   ```
   **Solution**: Kiểm tra microphone và `pip install sounddevice`

4. **Low detection accuracy**
   **Solution**: Điều chỉnh `WAKEWORD_CONFIDENCE_THRESHOLD` trong config.json

### **Debug Commands**
```bash
# Check model files
ls -la 06_training/models/

# Test audio devices
python -c "import sounddevice; print(sounddevice.query_devices())"

# Test PyTorch
python -c "import torch; print(torch.__version__)"
```

## 🎉 **Kết quả**

### **✅ Đã hoàn thành**
- [x] Tích hợp CNN wakeword detection vào ASR
- [x] Sử dụng model "Hây ka ka" từ 06_training
- [x] WebSocket integration với wakeword state
- [x] Comprehensive logging và monitoring
- [x] Test script và documentation
- [x] Configurable parameters

### **🎯 Hiệu quả**
- **Wakeword Detection**: Sử dụng CNN model có độ chính xác cao
- **ASR Efficiency**: Chỉ xử lý khi cần thiết (sau wakeword)
- **Real-time**: Continuous monitoring với latency thấp
- **Robust**: Error handling và state management tốt

**🎤 Hệ thống sẵn sàng để detect "Hây ka ka" và kích hoạt ASR!**
