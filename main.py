#!/usr/bin/env python3
import asyncio
import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

import numpy as np
import requests
import sherpa_onnx
import soundfile as sf
import websockets

import sys
import io

# <PERSON><PERSON><PERSON> bảo stdout luôn hỗ trợ UTF-8 (fix UnicodeEncodeError)
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import CNN wakeword detection
from wakeword.manager import WakewordManager, create_wakeword_manager

# --- Load config.json ---
config_path = Path(__file__).parent / "config.json"
with open(config_path, "r", encoding="utf-8") as f:
    cfg = json.load(f)

SMC_API_URL = cfg["SMC_API_URL"]
ENCODER_PATH = cfg["RECOGNIZER_ENCODER"]
DECODER_PATH = cfg["RECOGNIZER_DECODER"]
JOINER_PATH = cfg["RECOGNIZER_JOINER"]
TOKENS_PATH = cfg["RECOGNIZER_TOKENS"]
PORT = int(cfg["PORT"])

# --- Wakeword Configuration ---
WAKEWORD_ENABLED = cfg.get("WAKEWORD_ENABLED", True)
WAKEWORD_CONFIDENCE_THRESHOLD = cfg.get("WAKEWORD_CONFIDENCE_THRESHOLD", 0.5)
WAKEWORD_DETECTION_TIMEOUT = cfg.get("WAKEWORD_DETECTION_TIMEOUT", 5.0)

# Global wakeword manager
wakeword_manager = None

# --- Logger setup ---
def setup_logger(name="log/log-streaming-server"):
    now = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    log_path = f"{name}-{now}.txt"
    Path(log_path).parent.mkdir(parents=True, exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,  # Back to INFO level
        format="%(asctime)s %(levelname)s [%(filename)s:%(lineno)d] %(message)s",
        handlers=[
            logging.FileHandler(log_path, encoding="utf-8"),
            logging.StreamHandler(),
        ],
    )

# --- Call API (sync) ---
def call_api_in_thread(text: str):
    try:
        response = requests.post(
            SMC_API_URL,
            headers={'Content-Type': 'application/json'},
            json={"text": text},
            timeout=5,
        )
        print("API response:", response.text)
    except Exception as e:
        print("API call failed:", e)

# >>> CHANGED: helper chạy hàm blocking trong thread (Python 3.8-safe)
async def run_in_thread(func, *args, **kwargs):
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, lambda: func(*args, **kwargs))

# --- Sherpa Recognizer ---
def create_recognizer():
    return sherpa_onnx.OnlineRecognizer.from_transducer(
        tokens=TOKENS_PATH,
        encoder=ENCODER_PATH,
        decoder=DECODER_PATH,
        joiner=JOINER_PATH,
        num_threads=2,
        sample_rate=16000,
        feature_dim=80,
        decoding_method='greedy_search',
        max_active_paths=4,
        hotwords_score=1.5,
        enable_endpoint_detection=True,
        rule1_min_trailing_silence=2.4,
        rule2_min_trailing_silence=1.2,
        rule3_min_utterance_length=20,
        provider='cpu',
        modeling_unit='cjkchar',
    )

# --- Main WebSocket Server ---
class StreamingServer:
    def __init__(self, recognizer, port=8004, wakeword_manager=None):
        self.recognizer = recognizer
        self.sample_rate = int(recognizer.config.feat_config.sampling_rate)
        self.nn_pool = ThreadPoolExecutor(max_workers=1)
        self.stream_queue = asyncio.Queue()
        self.port = port
        self.max_active_connections = 10
        self.current_active_connections = 0
        self.wakeword_manager = wakeword_manager
        self.wakeword_detected = False
        self.waiting_for_wakeword = True if wakeword_manager else False
        self.last_heartbeat = time.time()
        self.heartbeat_interval = 60  # 1 minute
        self.conversation_end_keywords = ["kết thúc hội thoại", "kết thúc", "tạm biệt", "bye", "stop"]

    async def stream_consumer_task(self):
        while True:
            batch = []
            try:
                while len(batch) < 3:
                    item = self.stream_queue.get_nowait()
                    if self.recognizer.is_ready(item[0]):
                        batch.append(item)
            except asyncio.QueueEmpty:
                await asyncio.sleep(0.01)
            if not batch:
                continue
            stream_list = [b[0] for b in batch]
            future_list = [b[1] for b in batch]
            await asyncio.get_running_loop().run_in_executor(
                self.nn_pool, self.recognizer.decode_streams, stream_list
            )
            for f in future_list:
                self.stream_queue.task_done()
                f.set_result(None)

    async def compute_and_decode(self, stream):
        future = asyncio.get_running_loop().create_future()
        await self.stream_queue.put((stream, future))
        await future

    async def on_wakeword_detected(self, keyword: str):
        """Callback when wakeword is detected"""
        logging.info(f"🎤 CNN WAKEWORD DETECTED: '{keyword}' - ASR is now active")
        logging.info(f"📊 Wakeword Stats: {self.wakeword_manager.get_stats() if self.wakeword_manager else 'No manager'}")

        self.wakeword_detected = True
        self.waiting_for_wakeword = False

        # Reset wakeword state after some time to allow re-detection
        asyncio.create_task(self.reset_wakeword_state_after_delay())

    def check_conversation_end(self, text: str) -> bool:
        """Check if the text contains conversation end keywords"""
        text_lower = text.lower().strip()
        for keyword in self.conversation_end_keywords:
            if keyword in text_lower:
                return True
        return False

    def reset_to_wakeword_mode(self):
        """Reset to wakeword detection mode"""
        if self.wakeword_manager:
            self.wakeword_manager.reset_asr_state()
        self.waiting_for_wakeword = True
        self.wakeword_detected = False
        logging.info("🔄 Conversation ended - Ready for next wakeword detection")

    async def reset_wakeword_state_after_delay(self, delay: float = 30.0):
        """Reset wakeword state after delay to allow re-detection (increased to 30s)"""
        await asyncio.sleep(delay)
        self.reset_to_wakeword_mode()

    async def handle_connection(self, socket):
        try:
            await self.handle_connection_impl(socket)
        finally:
            self.current_active_connections -= 1

    async def handle_connection_impl(self, socket):
        stream = self.recognizer.create_stream()
        segment = 0
        all_samples = []
        max_samples_buffer = 1000000  # Limit buffer size to prevent memory leak

        # Send initial status about wakeword
        if self.waiting_for_wakeword:
            await socket.send(json.dumps({
                "status": "waiting_for_wakeword",
                "message": "Waiting for wake word detection...",
                "keywords": ["Hây ka ka"] if self.wakeword_manager else []
            }))

        while True:
            msg = await socket.recv()
            if msg == "Done":
                break

            # Heartbeat log every minute
            current_time = time.time()
            if current_time - self.last_heartbeat >= self.heartbeat_interval:
                self.last_heartbeat = current_time
                wakeword_status = "ACTIVE" if self.wakeword_manager and self.wakeword_manager.is_active else "INACTIVE"
                logging.info(f"💓 Service heartbeat - Wakeword: {wakeword_status}, Connections: {self.current_active_connections}")

            # Process audio for wakeword detection even when waiting
            # Handle Android 8-channel audio format
            try:
                # Parse Android 8-channel audio format
                raw_data = np.frombuffer(msg, dtype=np.int16)

                if len(raw_data) % 8 == 0:  # Should be divisible by 8 channels
                    # Reshape to 8 channels and extract channel 3 (0-indexed)
                    multi_channel = raw_data.reshape(-1, 8)
                    data = multi_channel[:, 3]  # Use channel 3
                else:
                    # Fallback: treat as single channel
                    data = raw_data

                samples = data.astype(np.float32) / 32768.0
                all_samples.append(samples)

                # Prevent memory leak by limiting buffer size
                total_samples = sum(len(s) for s in all_samples)
                if total_samples > max_samples_buffer:
                    # Keep only recent samples
                    all_samples = all_samples[-100:]  # Keep last 100 chunks

                # Process audio for wakeword detection if waiting
                if self.waiting_for_wakeword and self.wakeword_manager and self.wakeword_manager.detector:
                    wakeword_detected, confidence = self.wakeword_manager.detector.process_audio_chunk(samples)
                    if wakeword_detected:
                        logging.info(f"🎤 Wakeword detected from Android audio (confidence: {confidence:.3f})")
                        # Manually trigger state change for immediate effect
                        self.wakeword_detected = True
                        self.waiting_for_wakeword = False
                        logging.info(f"🔄 State changed: waiting_for_wakeword={self.waiting_for_wakeword}, wakeword_detected={self.wakeword_detected}")
                        # Wakeword callback will be triggered automatically

            except Exception as e:
                logging.error(f"❌ Error processing audio data: {e}")

            # If still waiting for wakeword after processing, send status and continue
            if self.waiting_for_wakeword and not self.wakeword_detected:
                await socket.send(json.dumps({
                    "status": "waiting_for_wakeword",
                    "message": "Still waiting for wake word..."
                }))
                continue

            # Continue with ASR processing (wakeword already detected)
            stream.accept_waveform(sample_rate=self.sample_rate, waveform=samples)

            while self.recognizer.is_ready(stream):
                await self.compute_and_decode(stream)
                result = self.recognizer.get_result(stream)

                # Send result with wakeword status
                response = {
                    "text": result,
                    "segment": segment,
                    "wakeword_detected": self.wakeword_detected,
                    "status": "processing"
                }
                await socket.send(json.dumps(response))

                if self.recognizer.is_endpoint(stream):
                    self.recognizer.reset(stream)
                    final_audio = np.concatenate(all_samples)
                    os.makedirs("recorded_audio", exist_ok=True)
                    sf.write(f"recorded_audio/segment_{segment}.wav", final_audio, self.sample_rate)
                    all_samples = []
                    if result.strip():
                        logging.info(f"🎯 ASR RESULT: '{result}' (after wakeword)")

                        # Check if conversation should end
                        if self.check_conversation_end(result):
                            logging.info(f"🔚 Conversation end detected: '{result}' - Switching to wakeword mode")
                            self.reset_to_wakeword_mode()

                        # >>> CHANGED: dùng run_in_thread thay vì asyncio.to_thread
                        await run_in_thread(call_api_in_thread, result)
                    segment += 1

        stream.input_finished()
        while self.recognizer.is_ready(stream):
            await self.compute_and_decode(stream)
        result = self.recognizer.get_result(stream)
        message = json.dumps({"text": result, "segment": segment}, ensure_ascii=False)
        await socket.send(message)  # Gửi dưới dạng string, WebSocket tự dùng UTF-8

    async def run(self):
        asyncio.create_task(self.stream_consumer_task())

        # Initialize wakeword detection if available (WebSocket mode)
        if self.wakeword_manager:
            success = self.wakeword_manager.start()
            if success:
                logging.info("✅ Wakeword detection initialized for WebSocket audio")
            else:
                logging.warning("⚠️ Failed to initialize wakeword detection, continuing without it")
                self.waiting_for_wakeword = False

        async with websockets.serve(
            self.handle_connection,
            host="",
            port=self.port,
            max_size=1048576,
            max_queue=32,
        ):
            logging.info(f"🚀 Streaming server started at port {self.port}")
            if self.wakeword_manager:
                logging.info(f"🎤 Wakeword detection active with keyword: 'Hây ka ka' (WebSocket mode)")
            await asyncio.Future()

def main():
    global wakeword_manager

    setup_logger()
    recognizer = create_recognizer()

    # Initialize CNN wakeword manager if enabled
    if WAKEWORD_ENABLED:
        try:
            wakeword_manager = WakewordManager(
                asr_callback=None,  # Will be set by server
                confidence_threshold=WAKEWORD_CONFIDENCE_THRESHOLD,
                detection_timeout=WAKEWORD_DETECTION_TIMEOUT,
                enabled=True
            )
            logging.info("✅ CNN Wakeword manager initialized")
        except Exception as e:
            logging.error(f"❌ Failed to initialize CNN wakeword manager: {e}")
            wakeword_manager = None
    else:
        logging.info("ℹ️ CNN Wakeword detection disabled in config")
        wakeword_manager = None

    # Create server with wakeword manager
    server = StreamingServer(recognizer, port=PORT, wakeword_manager=wakeword_manager)

    # Set the ASR callback for wakeword manager
    if wakeword_manager:
        wakeword_manager.asr_callback = server.on_wakeword_detected

    try:
        asyncio.run(server.run())
    except KeyboardInterrupt:
        logging.info("🛑 Server stopped by user")
    finally:
        # Cleanup wakeword manager
        if wakeword_manager:
            wakeword_manager.stop()
            logging.info("🧹 CNN Wakeword manager stopped")

if __name__ == "__main__":
    main()
